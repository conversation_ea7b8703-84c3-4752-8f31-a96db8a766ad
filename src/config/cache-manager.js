// 配置缓存管理器
// 处理配置的缓存、过期和监听机制

export class ConfigCacheManager {
  constructor() {
    this.cache = new Map();
    this.listeners = new Map();
    this.cacheTimestamps = new Map();
    this.cacheExpiration = 5 * 60 * 1000; // 5分钟缓存过期时间
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   */
  set(key, value) {
    this.cache.set(key, value);
    this.cacheTimestamps.set(key, Date.now());
    console.log(`📦 配置缓存已设置: ${key}`);
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存值或null
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null;
    }

    // 检查缓存是否过期
    if (this.isExpired(key)) {
      this.delete(key);
      console.log(`⏰ 配置缓存已过期: ${key}`);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在有效缓存
   */
  has(key) {
    if (!this.cache.has(key)) {
      return false;
    }

    if (this.isExpired(key)) {
      this.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    this.cacheTimestamps.delete(key);
    console.log(`🗑️ 配置缓存已删除: ${key}`);
  }

  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear();
    this.cacheTimestamps.clear();
    console.log('🧹 所有配置缓存已清除');
  }

  /**
   * 检查缓存是否过期
   * @param {string} key - 缓存键
   * @returns {boolean} 是否过期
   */
  isExpired(key) {
    const timestamp = this.cacheTimestamps.get(key);
    if (!timestamp) return true;
    
    return Date.now() - timestamp > this.cacheExpiration;
  }

  /**
   * 设置缓存过期时间
   * @param {number} milliseconds - 过期时间（毫秒）
   */
  setCacheExpiration(milliseconds) {
    this.cacheExpiration = milliseconds;
    console.log(`⏱️ 缓存过期时间已设置为: ${milliseconds}ms`);
  }

  /**
   * 添加配置变更监听器
   * @param {string} section - 配置部分
   * @param {Function} callback - 回调函数
   * @returns {string} 监听器ID
   */
  addListener(section, callback) {
    if (!this.listeners.has(section)) {
      this.listeners.set(section, new Map());
    }

    const listenerId = `${section}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.listeners.get(section).set(listenerId, callback);
    
    console.log(`👂 配置监听器已添加: ${section} (${listenerId})`);
    return listenerId;
  }

  /**
   * 移除配置变更监听器
   * @param {string} section - 配置部分
   * @param {string} listenerId - 监听器ID
   * @returns {boolean} 是否成功移除
   */
  removeListener(section, listenerId) {
    const sectionListeners = this.listeners.get(section);
    if (!sectionListeners) return false;

    const removed = sectionListeners.delete(listenerId);
    if (removed) {
      console.log(`🔇 配置监听器已移除: ${section} (${listenerId})`);
    }

    // 如果该部分没有监听器了，清理空的Map
    if (sectionListeners.size === 0) {
      this.listeners.delete(section);
    }

    return removed;
  }

  /**
   * 通知配置变更
   * @param {string} section - 配置部分
   * @param {any} data - 变更数据
   */
  notifyChange(section, data) {
    const sectionListeners = this.listeners.get(section);
    if (!sectionListeners) return;

    console.log(`📢 通知配置变更: ${section}`, data);

    sectionListeners.forEach((callback, listenerId) => {
      try {
        callback(section, data);
      } catch (error) {
        console.error(`配置监听器执行失败 (${listenerId}):`, error);
      }
    });
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 缓存统计
   */
  getStats() {
    const now = Date.now();
    const stats = {
      totalEntries: this.cache.size,
      expiredEntries: 0,
      validEntries: 0,
      listeners: 0,
      cacheExpiration: this.cacheExpiration
    };

    // 统计过期和有效的缓存条目
    this.cacheTimestamps.forEach((timestamp, key) => {
      if (now - timestamp > this.cacheExpiration) {
        stats.expiredEntries++;
      } else {
        stats.validEntries++;
      }
    });

    // 统计监听器数量
    this.listeners.forEach(sectionListeners => {
      stats.listeners += sectionListeners.size;
    });

    return stats;
  }

  /**
   * 清理过期缓存
   * @returns {number} 清理的条目数
   */
  cleanupExpired() {
    const expiredKeys = [];
    const now = Date.now();

    this.cacheTimestamps.forEach((timestamp, key) => {
      if (now - timestamp > this.cacheExpiration) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => this.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`🧹 已清理 ${expiredKeys.length} 个过期缓存条目`);
    }

    return expiredKeys.length;
  }

  /**
   * 预热缓存
   * @param {Array<string>} keys - 要预热的缓存键
   * @param {Function} loader - 加载函数
   */
  async warmup(keys, loader) {
    console.log(`🔥 开始预热缓存: ${keys.join(', ')}`);
    
    const promises = keys.map(async (key) => {
      try {
        if (!this.has(key)) {
          const value = await loader(key);
          this.set(key, value);
        }
      } catch (error) {
        console.error(`预热缓存失败 (${key}):`, error);
      }
    });

    await Promise.all(promises);
    console.log('✅ 缓存预热完成');
  }

  /**
   * 获取所有缓存键
   * @returns {Array<string>} 缓存键数组
   */
  getKeys() {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取所有监听的配置部分
   * @returns {Array<string>} 配置部分数组
   */
  getListenedSections() {
    return Array.from(this.listeners.keys());
  }
}

// 创建全局缓存管理器实例
export const configCacheManager = new ConfigCacheManager();
