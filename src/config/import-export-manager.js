// 配置导入导出管理器
// 处理配置的导入、导出、备份和迁移功能

import { configManager } from './config-manager.js';
import { securityManager } from './security-manager.js';

export class ConfigImportExportManager {
  constructor() {
    this.supportedVersions = ['1.0', '1.1'];
    this.currentVersion = '1.1';
  }

  /**
   * 导出所有配置
   * @param {object} options - 导出选项
   * @returns {Promise<object>} 导出的配置数据
   */
  async exportAllConfigs(options = {}) {
    const {
      includeSecrets = false,
      includeCache = false,
      format = 'json',
      compress = false
    } = options;

    try {
      console.log('🔄 开始导出配置...');

      const exportData = {
        version: this.currentVersion,
        timestamp: Date.now(),
        metadata: {
          exportedBy: 'Save to Flomo Extension',
          includeSecrets,
          includeCache
        },
        configs: {}
      };

      // 导出基础配置
      const sections = ['ai', 'flomo', 'sidepanel', 'ui'];
      for (const section of sections) {
        try {
          const config = await configManager.getConfig(section);
          exportData.configs[section] = this.sanitizeConfigForExport(config, includeSecrets);
        } catch (error) {
          console.warn(`导出 ${section} 配置失败:`, error);
          exportData.configs[section] = { error: error.message };
        }
      }

      // 导出安全信息（如果需要）
      if (includeSecrets) {
        exportData.security = await this.exportSecurityData();
      }

      // 导出缓存信息（如果需要）
      if (includeCache) {
        exportData.cache = await this.exportCacheData();
      }

      // 格式化输出
      const formattedData = this.formatExportData(exportData, format, compress);
      
      console.log('✅ 配置导出完成');
      return formattedData;
    } catch (error) {
      console.error('❌ 配置导出失败:', error);
      throw new Error(`配置导出失败: ${error.message}`);
    }
  }

  /**
   * 导入配置
   * @param {string|object} importData - 导入的数据
   * @param {object} options - 导入选项
   * @returns {Promise<object>} 导入结果
   */
  async importConfigs(importData, options = {}) {
    const {
      overwrite = false,
      validateOnly = false,
      includeSecrets = false,
      backup = true
    } = options;

    try {
      console.log('🔄 开始导入配置...');

      // 解析导入数据
      const parsedData = this.parseImportData(importData);
      
      // 验证数据格式
      const validation = this.validateImportData(parsedData);
      if (!validation.valid) {
        throw new Error(`导入数据验证失败: ${validation.error}`);
      }

      if (validateOnly) {
        return { valid: true, message: '导入数据验证通过' };
      }

      // 创建备份（如果需要）
      let backupData = null;
      if (backup) {
        backupData = await this.createBackup();
      }

      const importResult = {
        success: true,
        imported: [],
        failed: [],
        warnings: [],
        backup: backupData
      };

      // 导入配置
      for (const [section, config] of Object.entries(parsedData.configs || {})) {
        try {
          if (config.error) {
            importResult.warnings.push(`${section}: ${config.error}`);
            continue;
          }

          const success = await this.importSectionConfig(section, config, overwrite);
          if (success) {
            importResult.imported.push(section);
          } else {
            importResult.failed.push(section);
          }
        } catch (error) {
          console.error(`导入 ${section} 配置失败:`, error);
          importResult.failed.push(section);
        }
      }

      // 导入安全数据（如果需要且存在）
      if (includeSecrets && parsedData.security) {
        try {
          await this.importSecurityData(parsedData.security);
          importResult.imported.push('security');
        } catch (error) {
          console.error('导入安全数据失败:', error);
          importResult.failed.push('security');
        }
      }

      console.log('✅ 配置导入完成:', importResult);
      return importResult;
    } catch (error) {
      console.error('❌ 配置导入失败:', error);
      throw new Error(`配置导入失败: ${error.message}`);
    }
  }

  /**
   * 创建配置备份
   * @returns {Promise<object>} 备份数据
   */
  async createBackup() {
    try {
      const backupData = await this.exportAllConfigs({
        includeSecrets: true,
        includeCache: false
      });

      const backupInfo = {
        id: `backup_${Date.now()}`,
        timestamp: Date.now(),
        data: backupData
      };

      // 保存备份到本地存储
      await chrome.storage.local.set({
        [`config_backup_${backupInfo.id}`]: backupInfo
      });

      console.log('✅ 配置备份已创建:', backupInfo.id);
      return backupInfo;
    } catch (error) {
      console.error('创建配置备份失败:', error);
      throw error;
    }
  }

  /**
   * 恢复配置备份
   * @param {string} backupId - 备份ID
   * @returns {Promise<object>} 恢复结果
   */
  async restoreBackup(backupId) {
    try {
      const result = await chrome.storage.local.get(`config_backup_${backupId}`);
      const backupInfo = result[`config_backup_${backupId}`];

      if (!backupInfo) {
        throw new Error(`备份不存在: ${backupId}`);
      }

      console.log('🔄 开始恢复备份:', backupId);

      const restoreResult = await this.importConfigs(backupInfo.data, {
        overwrite: true,
        includeSecrets: true,
        backup: false // 恢复时不再创建备份
      });

      console.log('✅ 备份恢复完成');
      return restoreResult;
    } catch (error) {
      console.error('❌ 备份恢复失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有备份
   * @returns {Promise<Array>} 备份列表
   */
  async listBackups() {
    try {
      const allData = await chrome.storage.local.get();
      const backups = [];

      for (const [key, value] of Object.entries(allData)) {
        if (key.startsWith('config_backup_')) {
          backups.push({
            id: value.id,
            timestamp: value.timestamp,
            date: new Date(value.timestamp).toLocaleString(),
            size: JSON.stringify(value.data).length
          });
        }
      }

      // 按时间戳降序排序
      backups.sort((a, b) => b.timestamp - a.timestamp);
      return backups;
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return [];
    }
  }

  /**
   * 删除备份
   * @param {string} backupId - 备份ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteBackup(backupId) {
    try {
      await chrome.storage.local.remove(`config_backup_${backupId}`);
      console.log('✅ 备份已删除:', backupId);
      return true;
    } catch (error) {
      console.error('删除备份失败:', error);
      return false;
    }
  }

  /**
   * 清理过期备份
   * @param {number} maxAge - 最大保留时间（毫秒）
   * @returns {Promise<number>} 清理的备份数量
   */
  async cleanupOldBackups(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
    try {
      const backups = await this.listBackups();
      const now = Date.now();
      let cleanedCount = 0;

      for (const backup of backups) {
        if (now - backup.timestamp > maxAge) {
          await this.deleteBackup(backup.id);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`✅ 已清理 ${cleanedCount} 个过期备份`);
      }

      return cleanedCount;
    } catch (error) {
      console.error('清理过期备份失败:', error);
      return 0;
    }
  }

  /**
   * 净化配置用于导出
   * @param {object} config - 配置对象
   * @param {boolean} includeSecrets - 是否包含敏感信息
   * @returns {object} 净化后的配置
   */
  sanitizeConfigForExport(config, includeSecrets) {
    const sanitized = { ...config };

    if (!includeSecrets) {
      // 移除敏感信息
      delete sanitized.apiKey;
      delete sanitized.apiUrl;
      
      // 标记已移除敏感信息
      if (config.apiKey || config.apiUrl) {
        sanitized._secretsRemoved = true;
      }
    }

    return sanitized;
  }

  /**
   * 导出安全数据
   * @returns {Promise<object>} 安全数据
   */
  async exportSecurityData() {
    try {
      const securityStatus = await securityManager.getSecurityStatus();
      return {
        status: securityStatus,
        note: '安全数据已加密，只能在相同设备上导入'
      };
    } catch (error) {
      console.warn('导出安全数据失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 导出缓存数据
   * @returns {Promise<object>} 缓存数据
   */
  async exportCacheData() {
    try {
      const cacheStats = configManager.cacheManager.getStats();
      return {
        stats: cacheStats,
        note: '缓存数据仅用于诊断，不会在导入时恢复'
      };
    } catch (error) {
      console.warn('导出缓存数据失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 格式化导出数据
   * @param {object} data - 数据对象
   * @param {string} format - 格式类型
   * @param {boolean} compress - 是否压缩
   * @returns {string|object} 格式化后的数据
   */
  formatExportData(data, format, compress) {
    switch (format) {
      case 'json':
        return compress ? JSON.stringify(data) : JSON.stringify(data, null, 2);
      case 'object':
        return data;
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 解析导入数据
   * @param {string|object} data - 导入数据
   * @returns {object} 解析后的数据
   */
  parseImportData(data) {
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (error) {
        throw new Error('导入数据不是有效的JSON格式');
      }
    }
    
    if (typeof data === 'object' && data !== null) {
      return data;
    }
    
    throw new Error('导入数据格式无效');
  }

  /**
   * 验证导入数据
   * @param {object} data - 导入数据
   * @returns {object} 验证结果
   */
  validateImportData(data) {
    if (!data.version) {
      return { valid: false, error: '缺少版本信息' };
    }

    if (!this.supportedVersions.includes(data.version)) {
      return { 
        valid: false, 
        error: `不支持的版本: ${data.version}，支持的版本: ${this.supportedVersions.join(', ')}` 
      };
    }

    if (!data.configs || typeof data.configs !== 'object') {
      return { valid: false, error: '缺少配置数据' };
    }

    return { valid: true };
  }

  /**
   * 导入单个配置部分
   * @param {string} section - 配置部分
   * @param {object} config - 配置数据
   * @param {boolean} overwrite - 是否覆盖现有配置
   * @returns {Promise<boolean>} 导入是否成功
   */
  async importSectionConfig(section, config, overwrite) {
    try {
      if (!overwrite) {
        // 检查是否已有配置
        const existingConfig = await configManager.getConfig(section);
        if (Object.keys(existingConfig).length > 0) {
          console.warn(`${section} 配置已存在，跳过导入`);
          return false;
        }
      }

      const success = await configManager.setConfig(section, config);
      if (success) {
        console.log(`✅ ${section} 配置导入成功`);
      }
      
      return success;
    } catch (error) {
      console.error(`导入 ${section} 配置失败:`, error);
      return false;
    }
  }

  /**
   * 导入安全数据
   * @param {object} securityData - 安全数据
   * @returns {Promise<boolean>} 导入是否成功
   */
  async importSecurityData(securityData) {
    try {
      // 安全数据的导入需要特殊处理
      console.log('⚠️ 安全数据导入功能暂未实现');
      return false;
    } catch (error) {
      console.error('导入安全数据失败:', error);
      return false;
    }
  }
}

// 创建全局导入导出管理器实例
export const configImportExportManager = new ConfigImportExportManager();
