// TypeScript类型定义文件
// 为配置系统提供完整的类型注解

/**
 * AI服务提供商配置
 */
export interface AIProviderConfig {
  name: string;
  baseUrl: string;
  apiKey: string | null;
  models: string[];
  keyFormat: RegExp;
  description?: string;
}

/**
 * AI配置接口
 */
export interface AIConfig {
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  timeout: number;
  baseUrl?: string;
  apiKey?: string;
}

/**
 * Flomo配置接口
 */
export interface FlomoConfig {
  apiUrl: string;
  timeout: number;
}

/**
 * 侧边栏配置接口
 */
export interface SidepanelConfig {
  autoClose: boolean;
  autoCloseDelay: number;
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
}

/**
 * UI配置接口
 */
export interface UIConfig {
  language: 'zh-CN' | 'en-US';
  showTooltips: boolean;
  animationEnabled: boolean;
}

/**
 * 配置部分类型
 */
export type ConfigSection = 'ai' | 'flomo' | 'sidepanel' | 'ui';

/**
 * 配置对象类型映射
 */
export interface ConfigTypeMap {
  ai: AIConfig;
  flomo: FlomoConfig;
  sidepanel: SidepanelConfig;
  ui: UIConfig;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  error?: string | null;
  details?: ValidationDetail[];
  warnings?: ValidationWarning[];
}

/**
 * 验证详情接口
 */
export interface ValidationDetail {
  field: string;
  error: string;
  value: any;
  supportedModels?: string[];
}

/**
 * 验证警告接口
 */
export interface ValidationWarning {
  field: string;
  warning: string;
  value: any;
}

/**
 * 配置优先级枚举
 */
export enum ConfigPriority {
  ENVIRONMENT = 3,
  USER = 2,
  DEFAULTS = 1
}

/**
 * 存储键映射接口
 */
export interface StorageKeys {
  ai: string;
  flomo: string;
  sidepanel: string;
  ui: string;
}

/**
 * 配置模式接口
 */
export interface ConfigSchema {
  ai: {
    defaults: AIConfig;
    providers: Record<string, AIProviderConfig>;
    validation: Record<string, Function>;
  };
  flomo: {
    defaults: FlomoConfig;
    validation: Record<string, Function>;
  };
  sidepanel: {
    defaults: SidepanelConfig;
    validation: Record<string, Function>;
  };
  ui: {
    defaults: UIConfig;
    validation: Record<string, Function>;
  };
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalEntries: number;
  expiredEntries: number;
  validEntries: number;
  listeners: number;
  cacheExpiration: number;
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  section: string;
  error: string;
  timestamp: number;
  type: string;
  stack?: string;
}

/**
 * 恢复结果接口
 */
export interface RecoveryResult {
  success: boolean;
  strategy?: string;
  config?: any;
  message?: string;
  error?: ErrorInfo;
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  total: number;
  recentHour: number;
  dailyTotal: number;
  bySection: Record<string, number>;
  retryAttempts: Record<string, number>;
}

/**
 * 安全状态接口
 */
export interface SecurityStatus {
  initialized: boolean;
  hasEncryptionKey: boolean;
  storedKeys: Array<{
    provider: string;
    stored: boolean;
    expired: boolean;
  }>;
}

/**
 * 配置摘要接口
 */
export interface ConfigSummary {
  [section: string]: {
    configured: boolean;
    keys: string[];
    error?: string;
  };
}

/**
 * 配置变更监听器类型
 */
export type ConfigChangeListener = (section: string, data: any) => void;

/**
 * 配置管理器接口
 */
export interface IConfigManager {
  initialize(): Promise<void>;
  getConfig<T extends ConfigSection>(section: T): Promise<ConfigTypeMap[T]>;
  setConfig<T extends ConfigSection>(section: T, config: Partial<ConfigTypeMap[T]>): Promise<boolean>;
  resetConfig(section: ConfigSection): Promise<boolean>;
  validateApiConfig(section: ConfigSection, config: any): Promise<ValidationResult>;
  addListener(section: ConfigSection, callback: ConfigChangeListener): () => void;
  clearCache(): void;
  getConfigSummary(): Promise<ConfigSummary>;
}

/**
 * 安全管理器接口
 */
export interface ISecurityManager {
  initialize(): Promise<void>;
  encryptData(data: string): Promise<string>;
  decryptData(encryptedData: string): Promise<string>;
  validateApiKeyFormat(apiKey: string, provider: string): boolean;
  storeApiKey(provider: string, apiKey: string): Promise<boolean>;
  getApiKey(provider: string): Promise<string | null>;
  removeApiKey(provider: string): Promise<boolean>;
  getSecurityStatus(): Promise<SecurityStatus>;
}

/**
 * 缓存管理器接口
 */
export interface ICacheManager {
  set(key: string, value: any): void;
  get(key: string): any | null;
  has(key: string): boolean;
  delete(key: string): void;
  clear(): void;
  addListener(section: string, callback: ConfigChangeListener): string;
  removeListener(section: string, listenerId: string): boolean;
  notifyChange(section: string, data: any): void;
  getStats(): CacheStats;
}

/**
 * 验证管理器接口
 */
export interface IValidationManager {
  validateConfig(section: ConfigSection, config: any): ValidationResult;
  validateAIConfig(config: AIConfig): Promise<ValidationResult>;
  validateFlomoConfig(config: FlomoConfig): Promise<ValidationResult>;
  getDefaults(section: ConfigSection): any;
  sanitizeConfig(section: ConfigSection, config: any): any;
}

/**
 * 错误处理器接口
 */
export interface IErrorHandler {
  handleConfigLoadError(section: ConfigSection, error: Error, options?: any): Promise<RecoveryResult>;
  getErrorStats(): ErrorStats;
  clearErrorHistory(): void;
}

/**
 * Prompt模板类型
 */
export interface PromptTemplate {
  [feature: string]: {
    [type: string]: string;
  };
}

/**
 * 模板预设类型
 */
export interface TemplatePreset {
  feature: string;
  type: string;
}

/**
 * 模板预设映射
 */
export interface TemplatePresets {
  [category: string]: {
    [name: string]: TemplatePreset;
  };
}

/**
 * Prompt管理器接口
 */
export interface IPromptManager {
  render(template: string, variables: Record<string, any>): string;
  getTemplate(feature: string, type?: string): string | null;
  renderTemplate(feature: string, type: string, variables?: Record<string, any>): string | null;
  getAvailableFeatures(): string[];
  getAvailableTypes(feature: string): string[];
}

// 全局类型声明
declare global {
  interface Window {
    configManager?: IConfigManager;
    securityManager?: ISecurityManager;
  }
}
