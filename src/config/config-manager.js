// 统一配置管理器
// 处理配置的获取、设置、验证和优先级管理

import { CONFIG_SCHEMA, CONFIG_PRIORITY, STORAGE_KEYS, ConfigValidator } from './config-schema.js';
import { securityManager } from './security-manager.js';
import { configCacheManager } from './cache-manager.js';
import { configValidationManager } from './validation-manager.js';
import { configErrorHandler } from './error-handler.js';

export class ConfigManager {
  constructor() {
    // 使用模块化的管理器
    this.cacheManager = configCacheManager;
    this.validationManager = configValidationManager;
    this.errorHandler = configErrorHandler;
    this.initialized = false;
  }

  /**
   * 初始化配置管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 初始化安全管理器
      await securityManager.initialize();

      // 预加载所有配置
      await this.preloadConfigs();
      this.initialized = true;
      console.log('✅ 配置管理器初始化完成');
    } catch (error) {
      console.error('❌ 配置管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 预加载所有配置到缓存
   */
  async preloadConfigs() {
    const sections = Object.keys(CONFIG_SCHEMA);
    const promises = sections.map(section => this.getConfig(section));
    await Promise.all(promises);
  }

  /**
   * 获取指定部分的配置
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 配置对象
   */
  async getConfig(section) {
    // 检查缓存
    if (this.cacheManager.has(section)) {
      return this.cacheManager.get(section);
    }

    try {
      const config = await this.buildConfig(section);

      // 保存成功的配置作为备份
      await this.saveSuccessfulConfig(section, config);

      this.cacheManager.set(section, config);
      return config;
    } catch (error) {
      console.error(`获取 ${section} 配置失败:`, error);

      // 使用错误处理器进行恢复
      const recoveryResult = await this.errorHandler.handleConfigLoadError(
        section,
        error,
        { retryLoader: (s) => this.buildConfig(s) }
      );

      const finalConfig = recoveryResult.config || this.validationManager.getDefaults(section);
      this.cacheManager.set(section, finalConfig);
      return finalConfig;
    }
  }

  /**
   * 构建配置（按优先级合并）
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 合并后的配置
   */
  async buildConfig(section) {
    const configs = [];

    // 1. 默认配置（最低优先级）
    const defaults = ConfigValidator.getDefaults(section);
    configs.push({ priority: CONFIG_PRIORITY.DEFAULTS, config: defaults });

    // 2. 用户配置
    const userConfig = await this.getUserConfig(section);
    if (userConfig && Object.keys(userConfig).length > 0) {
      configs.push({ priority: CONFIG_PRIORITY.USER, config: userConfig });
    }

    // 3. 环境配置（最高优先级）
    const envConfig = this.getEnvironmentConfig(section);
    if (envConfig && Object.keys(envConfig).length > 0) {
      configs.push({ priority: CONFIG_PRIORITY.ENVIRONMENT, config: envConfig });
    }

    // 按优先级排序并合并
    configs.sort((a, b) => a.priority - b.priority);
    const mergedConfig = configs.reduce((result, item) => {
      return { ...result, ...item.config };
    }, {});

    // 特殊处理 AI 配置：根据 provider 动态合并 provider 配置
    if (section === 'ai') {
      return await this.buildAIConfig(mergedConfig);
    }

    return mergedConfig;
  }

  /**
   * 构建 AI 配置（动态合并 provider 配置）
   * @param {object} baseConfig - 基础配置
   * @returns {Promise<object>} 完整的 AI 配置
   */
  async buildAIConfig(baseConfig) {
    const provider = baseConfig.provider;
    if (!provider) {
      throw new Error('AI 配置中缺少 provider 字段');
    }

    const providerConfig = CONFIG_SCHEMA.ai.providers[provider];
    if (!providerConfig) {
      throw new Error(`不支持的 AI 提供商: ${provider}`);
    }

    // 获取用户设置的 provider apiKey
    const userProviderApiKey = await this.getProviderApiKey(provider);

    // 合并 provider 配置到基础配置中
    const finalConfig = {
      ...baseConfig,
      baseUrl: providerConfig.baseUrl,
      apiKey: userProviderApiKey || providerConfig.apiKey
    };

    return finalConfig;
  }

  /**
   * 获取 provider 的用户设置 API Key
   * @param {string} provider - 提供商名称
   * @returns {Promise<string|null>} API Key 或 null
   */
  async getProviderApiKey(provider) {
    try {
      // 优先从安全管理器获取加密存储的API密钥
      const secureApiKey = await securityManager.getApiKey(provider);
      if (secureApiKey) {
        return secureApiKey;
      }

      // 兼容旧版本：从普通存储获取（不推荐）
      const providerStorageKey = 'providerConfigs';
      const result = await chrome.storage.sync.get(providerStorageKey);
      const providerConfigs = result[providerStorageKey] || {};
      const legacyApiKey = providerConfigs[provider]?.apiKey;

      if (legacyApiKey) {
        console.warn(`检测到 ${provider} 的未加密API密钥，建议重新设置以启用加密存储`);
        return legacyApiKey;
      }

      return null;
    } catch (error) {
      console.warn(`获取 ${provider} API Key 失败:`, error);
      return null;
    }
  }

  /**
   * 安全设置 provider 的 API Key
   * @param {string} provider - 提供商名称
   * @param {string} apiKey - API密钥
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setProviderApiKey(provider, apiKey) {
    try {
      // 验证提供商是否支持
      if (!CONFIG_SCHEMA.ai.providers[provider]) {
        throw new Error(`不支持的AI提供商: ${provider}`);
      }

      // 使用安全管理器存储加密的API密钥
      const success = await securityManager.storeApiKey(provider, apiKey);

      if (success) {
        // 清除缓存以确保下次获取时使用新密钥
        this.cacheManager.delete('ai');

        // 通知配置变更
        this.cacheManager.notifyChange('ai', { provider, apiKeyUpdated: true });

        console.log(`✅ ${provider} API密钥已安全设置`);
      }

      return success;
    } catch (error) {
      console.error(`设置 ${provider} API密钥失败:`, error);
      throw error;
    }
  }

  /**
   * 移除 provider 的 API Key
   * @param {string} provider - 提供商名称
   * @returns {Promise<boolean>} 移除是否成功
   */
  async removeProviderApiKey(provider) {
    try {
      const success = await securityManager.removeApiKey(provider);

      if (success) {
        // 清除缓存
        this.cacheManager.delete('ai');

        // 通知配置变更
        this.cacheManager.notifyChange('ai', { provider, apiKeyRemoved: true });

        console.log(`✅ ${provider} API密钥已移除`);
      }

      return success;
    } catch (error) {
      console.error(`移除 ${provider} API密钥失败:`, error);
      return false;
    }
  }

  /**
   * 获取用户配置
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 用户配置
   */
  async getUserConfig(section) {
    const storageKey = STORAGE_KEYS[section];
    if (!storageKey) return {};

    try {
      const result = await chrome.storage.sync.get(storageKey);
      return result[storageKey] || {};
    } catch (error) {
      console.warn(`获取用户配置 ${section} 失败:`, error);
      return {};
    }
  }

  /**
   * 获取环境配置
   * @param {string} _section - 配置部分名称（已弃用）
   * @returns {object} 环境配置
   */
  getEnvironmentConfig(_section) {
    // 环境配置功能已移除，不再支持硬编码的环境配置
    // 所有配置都应该通过用户设置或默认值来管理
    return {};
  }

  /**
   * 设置用户配置
   * @param {string} section - 配置部分名称
   * @param {object} config - 配置对象
   * @param {boolean} validate - 是否验证配置
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setConfig(section, config, validate = true) {
    try {
      // 特殊处理 AI 配置：如果设置了 apiKey，需要更新到对应的 provider 中
      if (section === 'ai' && config.apiKey && config.provider) {
        await this.updateProviderApiKey(config.provider, config.apiKey);
        // 从用户配置中移除 apiKey，因为它现在存储在 provider 中
        const { apiKey, ...configWithoutApiKey } = config;
        config = configWithoutApiKey;
      }

      // 验证配置
      if (validate) {
        const validation = ConfigValidator.validateSection(section, config);
        if (!validation.valid) {
          throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
        }
      }

      // 保存到存储
      const storageKey = STORAGE_KEYS[section];
      if (storageKey) {
        await chrome.storage.sync.set({ [storageKey]: config });
      }

      // 清除缓存，强制重新构建配置
      this.cacheManager.delete(section);

      // 获取更新后的完整配置
      const updatedConfig = await this.getConfig(section);

      // 通知监听器
      this.notifyListeners(section, updatedConfig);

      console.log(`✅ ${section} 配置已更新`);
      return true;
    } catch (error) {
      console.error(`设置 ${section} 配置失败:`, error);
      throw error;
    }
  }

  /**
   * 更新 provider 的 API Key
   * @param {string} provider - 提供商名称
   * @param {string} apiKey - API Key
   */
  async updateProviderApiKey(provider, apiKey) {
    // 获取当前的 provider 配置存储
    const providerStorageKey = 'providerConfigs';
    const result = await chrome.storage.sync.get(providerStorageKey);
    const providerConfigs = result[providerStorageKey] || {};

    // 更新指定 provider 的 apiKey
    if (!providerConfigs[provider]) {
      providerConfigs[provider] = {};
    }
    providerConfigs[provider].apiKey = apiKey;

    // 保存回存储
    await chrome.storage.sync.set({ [providerStorageKey]: providerConfigs });

    console.log(`✅ ${provider} 的 API Key 已更新`);
  }

  /**
   * 获取特定配置项
   * @param {string} section - 配置部分名称
   * @param {string} key - 配置键名
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 配置值
   */
  async getConfigValue(section, key, defaultValue = null) {
    const config = await this.getConfig(section);
    return config[key] !== undefined ? config[key] : defaultValue;
  }

  /**
   * 设置特定配置项
   * @param {string} section - 配置部分名称
   * @param {string} key - 配置键名
   * @param {any} value - 配置值
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setConfigValue(section, key, value) {
    const currentConfig = await this.getUserConfig(section);
    const updatedConfig = { ...currentConfig, [key]: value };
    return await this.setConfig(section, updatedConfig);
  }

  /**
   * 重置配置到默认值
   * @param {string} section - 配置部分名称
   * @returns {Promise<boolean>} 重置是否成功
   */
  async resetConfig(section) {
    try {
      const storageKey = STORAGE_KEYS[section];
      if (storageKey) {
        await chrome.storage.sync.remove(storageKey);
      }

      // 清除缓存，强制重新构建
      this.cacheManager.delete(section);

      const defaultConfig = await this.getConfig(section);
      this.notifyListeners(section, defaultConfig);

      console.log(`✅ ${section} 配置已重置`);
      return true;
    } catch (error) {
      console.error(`重置 ${section} 配置失败:`, error);
      throw error;
    }
  }

  /**
   * 添加配置变更监听器
   * @param {string} section - 配置部分名称
   * @param {function} callback - 回调函数
   * @returns {function} 取消监听的函数
   */
  addListener(section, callback) {
    const listenerId = this.cacheManager.addListener(section, callback);

    // 返回取消监听的函数
    return () => {
      this.cacheManager.removeListener(section, listenerId);
    };
  }

  /**
   * 通知配置变更监听器
   * @param {string} section - 配置部分名称
   * @param {object} config - 新配置
   */
  notifyListeners(section, config) {
    this.cacheManager.notifyChange(section, config);
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cacheManager.clear();
    console.log('✅ 配置缓存已清除');
  }

  /**
   * 验证API配置
   * @param {string} section - 配置部分名称
   * @param {object} config - 配置对象
   * @returns {Promise<object>} 验证结果
   */
  async validateApiConfig(section, config) {
    if (section !== 'ai' && section !== 'flomo') {
      return { valid: true };
    }

    try {
      if (section === 'ai') {
        return await this.validationManager.validateAIConfig(config);
      } else if (section === 'flomo') {
        return await this.validationManager.validateFlomoConfig(config);
      }
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }



  /**
   * 保存成功的配置作为备份
   * @param {string} section - 配置部分
   * @param {object} config - 配置对象
   */
  async saveSuccessfulConfig(section, config) {
    try {
      const backupData = {
        config,
        timestamp: Date.now(),
        version: '1.0'
      };

      await chrome.storage.local.set({
        [`last_good_config_${section}`]: backupData,
        [`backup_config_${section}`]: config
      });
    } catch (error) {
      console.warn(`保存配置备份失败 [${section}]:`, error);
    }
  }

  /**
   * 获取所有配置的摘要信息
   * @returns {Promise<object>} 配置摘要
   */
  async getConfigSummary() {
    const summary = {};
    const sections = Object.keys(CONFIG_SCHEMA);

    for (const section of sections) {
      try {
        const config = await this.getConfig(section);
        summary[section] = {
          configured: Object.keys(config).length > 0,
          keys: Object.keys(config)
        };
      } catch (error) {
        summary[section] = {
          configured: false,
          error: error.message
        };
      }
    }

    return summary;
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager();
