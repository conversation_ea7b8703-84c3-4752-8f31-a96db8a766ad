// 安全配置管理器
// 处理敏感数据的加密存储和安全验证

export class SecurityManager {
  constructor() {
    this.encryptionKey = null;
    this.initialized = false;
  }

  /**
   * 初始化安全管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 生成或获取加密密钥
      await this.initializeEncryptionKey();
      this.initialized = true;
      console.log('✅ 安全管理器初始化完成');
    } catch (error) {
      console.error('❌ 安全管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化加密密钥
   */
  async initializeEncryptionKey() {
    try {
      // 尝试从存储中获取密钥
      const result = await chrome.storage.local.get('encryptionSalt');

      if (result.encryptionSalt) {
        this.encryptionKey = result.encryptionSalt;
      } else {
        // 生成新的加密盐值
        this.encryptionKey = this.generateEncryptionKey();
        await chrome.storage.local.set({ encryptionSalt: this.encryptionKey });
      }
    } catch (error) {
      console.error('初始化加密密钥失败:', error);
      // 使用临时密钥
      this.encryptionKey = this.generateEncryptionKey();
    }
  }

  /**
   * 生成加密密钥
   */
  generateEncryptionKey() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 加密敏感数据
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的数据
   */
  async encryptData(data) {
    if (!data || typeof data !== 'string') {
      return data;
    }

    try {
      // 使用增强的加密方法
      const encrypted = await this.enhancedEncrypt(data, this.encryptionKey);
      return encrypted;
    } catch (error) {
      console.error('数据加密失败:', error);
      // 回退到简单加密
      try {
        const fallbackEncrypted = this.xorEncrypt(data, this.encryptionKey);
        return btoa(fallbackEncrypted);
      } catch (fallbackError) {
        console.error('回退加密也失败:', fallbackError);
        return data; // 最后回退：返回原数据
      }
    }
  }

  /**
   * 解密敏感数据
   * @param {string} encryptedData - 加密的数据
   * @returns {string} 解密后的数据
   */
  async decryptData(encryptedData) {
    if (!encryptedData || typeof encryptedData !== 'string') {
      return encryptedData;
    }

    try {
      // 尝试增强解密
      if (encryptedData.startsWith('v1:')) {
        return await this.enhancedDecrypt(encryptedData, this.encryptionKey);
      } else {
        // 回退到简单解密
        const decoded = atob(encryptedData); // Base64解码
        return this.xorEncrypt(decoded, this.encryptionKey);
      }
    } catch (error) {
      console.error('数据解密失败:', error);
      // 尝试简单解密作为最后回退
      try {
        const decoded = atob(encryptedData);
        return this.xorEncrypt(decoded, this.encryptionKey);
      } catch (fallbackError) {
        console.error('回退解密也失败:', fallbackError);
        return encryptedData; // 解密失败时返回原数据
      }
    }
  }

  /**
   * 增强的加密方法
   * @param {string} data - 要加密的数据
   * @param {string} key - 加密密钥
   * @returns {Promise<string>} 加密后的数据
   */
  async enhancedEncrypt(data, key) {
    try {
      // 生成随机盐值
      const salt = crypto.getRandomValues(new Uint8Array(16));
      const saltHex = Array.from(salt, byte => byte.toString(16).padStart(2, '0')).join('');

      // 使用增强的XOR加密作为回退方案
      return this.enhancedXorEncrypt(data, key, saltHex);
    } catch (error) {
      console.warn('增强加密失败，使用简单加密:', error);
      throw error;
    }
  }

  /**
   * 增强的解密方法
   * @param {string} encryptedData - 加密的数据
   * @param {string} key - 解密密钥
   * @returns {Promise<string>} 解密后的数据
   */
  async enhancedDecrypt(encryptedData, key) {
    try {
      if (encryptedData.startsWith('v1:')) {
        // 增强XOR格式
        return this.enhancedXorDecrypt(encryptedData, key);
      } else {
        // 旧格式，使用简单解密
        throw new Error('使用旧格式，需要回退解密');
      }
    } catch (error) {
      console.warn('增强解密失败:', error);
      throw error;
    }
  }

  /**
   * 增强的XOR加密
   * @param {string} data - 数据
   * @param {string} key - 密钥
   * @param {string} salt - 盐值
   * @returns {string} 加密后的数据
   */
  enhancedXorEncrypt(data, key, salt) {
    // 使用盐值增强密钥
    const enhancedKey = this.hashString(key + salt);
    const encrypted = this.xorEncrypt(data, enhancedKey);
    return `v1:${salt}:${btoa(encrypted)}`;
  }

  /**
   * 增强的XOR解密
   * @param {string} encryptedData - 加密数据
   * @param {string} key - 密钥
   * @returns {string} 解密后的数据
   */
  enhancedXorDecrypt(encryptedData, key) {
    const parts = encryptedData.split(':');
    if (parts.length !== 3 || parts[0] !== 'v1') {
      throw new Error('增强XOR数据格式无效');
    }

    const [version, salt, encrypted] = parts;
    const enhancedKey = this.hashString(key + salt);
    const decoded = atob(encrypted);
    return this.xorEncrypt(decoded, enhancedKey);
  }

  /**
   * 简单哈希函数
   * @param {string} str - 要哈希的字符串
   * @returns {string} 哈希值
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * XOR加密/解密
   * @param {string} data - 数据
   * @param {string} key - 密钥
   * @returns {string} 处理后的数据
   */
  xorEncrypt(data, key) {
    let result = '';
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i);
      const keyCode = key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode ^ keyCode);
    }
    return result;
  }

  /**
   * 验证API密钥格式
   * @param {string} apiKey - API密钥
   * @param {string} provider - 提供商名称
   * @returns {boolean} 验证结果
   */
  validateApiKeyFormat(apiKey, provider) {
    if (!apiKey || !provider) return false;

    const patterns = {
      siliconflow: /^sk-[a-zA-Z0-9]{48}$/,
      openrouter: /^sk-or-[a-zA-Z0-9-]+$/,
      deepseek: /^sk-[a-zA-Z0-9]{32}$/,
      moonshot: /^sk-[a-zA-Z0-9]{48}$/
    };

    const pattern = patterns[provider];
    return pattern ? pattern.test(apiKey) : false;
  }

  /**
   * 安全存储API密钥
   * @param {string} provider - 提供商名称
   * @param {string} apiKey - API密钥
   * @returns {Promise<boolean>} 存储是否成功
   */
  async storeApiKey(provider, apiKey) {
    if (!this.validateApiKeyFormat(apiKey, provider)) {
      throw new Error(`无效的 ${provider} API密钥格式`);
    }

    try {
      const encryptedKey = await this.encryptData(apiKey);
      const storageKey = `encrypted_api_key_${provider}`;

      await chrome.storage.sync.set({
        [storageKey]: encryptedKey,
        [`${storageKey}_timestamp`]: Date.now()
      });

      console.log(`✅ ${provider} API密钥已安全存储`);
      return true;
    } catch (error) {
      console.error(`存储 ${provider} API密钥失败:`, error);
      return false;
    }
  }

  /**
   * 安全获取API密钥
   * @param {string} provider - 提供商名称
   * @returns {Promise<string|null>} API密钥或null
   */
  async getApiKey(provider) {
    try {
      const storageKey = `encrypted_api_key_${provider}`;
      const result = await chrome.storage.sync.get([storageKey, `${storageKey}_timestamp`]);

      if (!result[storageKey]) {
        return null;
      }

      // 检查密钥是否过期（可选功能）
      const timestamp = result[`${storageKey}_timestamp`];
      if (timestamp && this.isKeyExpired(timestamp)) {
        console.warn(`${provider} API密钥已过期`);
        await this.removeApiKey(provider);
        return null;
      }

      const decryptedKey = await this.decryptData(result[storageKey]);
      return decryptedKey;
    } catch (error) {
      console.error(`获取 ${provider} API密钥失败:`, error);
      return null;
    }
  }

  /**
   * 移除API密钥
   * @param {string} provider - 提供商名称
   * @returns {Promise<boolean>} 移除是否成功
   */
  async removeApiKey(provider) {
    try {
      const storageKey = `encrypted_api_key_${provider}`;
      await chrome.storage.sync.remove([storageKey, `${storageKey}_timestamp`]);
      console.log(`✅ ${provider} API密钥已移除`);
      return true;
    } catch (error) {
      console.error(`移除 ${provider} API密钥失败:`, error);
      return false;
    }
  }

  /**
   * 检查密钥是否过期
   * @param {number} timestamp - 时间戳
   * @returns {boolean} 是否过期
   */
  isKeyExpired(timestamp) {
    const expirationTime = 30 * 24 * 60 * 60 * 1000; // 30天
    return Date.now() - timestamp > expirationTime;
  }

  /**
   * 清除所有加密数据
   * @returns {Promise<boolean>} 清除是否成功
   */
  async clearAllEncryptedData() {
    try {
      const allData = await chrome.storage.sync.get();
      const keysToRemove = Object.keys(allData).filter(key =>
        key.startsWith('encrypted_api_key_') || key === 'encryptionSalt'
      );

      if (keysToRemove.length > 0) {
        await chrome.storage.sync.remove(keysToRemove);
        await chrome.storage.local.remove('encryptionSalt');
      }

      console.log('✅ 所有加密数据已清除');
      return true;
    } catch (error) {
      console.error('清除加密数据失败:', error);
      return false;
    }
  }

  /**
   * 获取安全状态摘要
   * @returns {Promise<object>} 安全状态信息
   */
  async getSecurityStatus() {
    const status = {
      initialized: this.initialized,
      hasEncryptionKey: !!this.encryptionKey,
      storedKeys: []
    };

    try {
      const allData = await chrome.storage.sync.get();
      const encryptedKeys = Object.keys(allData).filter(key =>
        key.startsWith('encrypted_api_key_') && !key.endsWith('_timestamp')
      );

      status.storedKeys = encryptedKeys.map(key => {
        const provider = key.replace('encrypted_api_key_', '');
        const timestamp = allData[`${key}_timestamp`];
        return {
          provider,
          stored: true,
          expired: timestamp ? this.isKeyExpired(timestamp) : false
        };
      });
    } catch (error) {
      console.error('获取安全状态失败:', error);
    }

    return status;
  }
}

// 创建全局安全管理器实例
export const securityManager = new SecurityManager();
