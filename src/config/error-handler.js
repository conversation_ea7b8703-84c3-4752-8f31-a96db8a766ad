// 配置错误处理管理器
// 处理配置相关的错误、回退策略和恢复机制

export class ConfigErrorHandler {
  constructor() {
    this.errorHistory = [];
    this.maxErrorHistory = 50;
    this.retryAttempts = new Map();
    this.maxRetryAttempts = 3;
    this.retryDelay = 1000; // 1秒
  }

  /**
   * 处理配置加载错误
   * @param {string} section - 配置部分
   * @param {Error} error - 错误对象
   * @param {object} options - 处理选项
   * @returns {Promise<object>} 处理结果
   */
  async handleConfigLoadError(section, error, options = {}) {
    const errorInfo = {
      section,
      error: error.message,
      timestamp: Date.now(),
      type: 'CONFIG_LOAD_ERROR',
      stack: error.stack
    };

    this.recordError(errorInfo);

    console.error(`❌ 配置加载失败 [${section}]:`, error);

    // 尝试恢复策略
    const recoveryResult = await this.attemptRecovery(section, error, options);
    
    if (recoveryResult.success) {
      console.log(`✅ 配置恢复成功 [${section}]:`, recoveryResult.strategy);
      return recoveryResult;
    }

    // 如果恢复失败，返回默认配置
    console.warn(`⚠️ 使用默认配置 [${section}]`);
    return {
      success: false,
      strategy: 'default_fallback',
      config: this.getDefaultConfig(section),
      error: errorInfo
    };
  }

  /**
   * 尝试配置恢复
   * @param {string} section - 配置部分
   * @param {Error} error - 错误对象
   * @param {object} options - 恢复选项
   * @returns {Promise<object>} 恢复结果
   */
  async attemptRecovery(section, error, options = {}) {
    const strategies = [
      'retry_load',
      'cache_fallback',
      'backup_restore',
      'partial_recovery',
      'reset_to_defaults'
    ];

    for (const strategy of strategies) {
      try {
        console.log(`🔄 尝试恢复策略: ${strategy} [${section}]`);
        
        const result = await this.executeRecoveryStrategy(strategy, section, error, options);
        
        if (result.success) {
          return {
            success: true,
            strategy,
            config: result.config,
            message: result.message
          };
        }
      } catch (recoveryError) {
        console.warn(`恢复策略失败 [${strategy}]:`, recoveryError.message);
      }
    }

    return { success: false };
  }

  /**
   * 执行恢复策略
   * @param {string} strategy - 恢复策略
   * @param {string} section - 配置部分
   * @param {Error} error - 原始错误
   * @param {object} options - 选项
   * @returns {Promise<object>} 执行结果
   */
  async executeRecoveryStrategy(strategy, section, error, options) {
    switch (strategy) {
      case 'retry_load':
        return await this.retryLoad(section, options);
      
      case 'cache_fallback':
        return await this.useCacheFallback(section);
      
      case 'backup_restore':
        return await this.restoreFromBackup(section);
      
      case 'partial_recovery':
        return await this.partialRecovery(section, error);
      
      case 'reset_to_defaults':
        return await this.resetToDefaults(section);
      
      default:
        throw new Error(`未知的恢复策略: ${strategy}`);
    }
  }

  /**
   * 重试加载配置
   * @param {string} section - 配置部分
   * @param {object} options - 选项
   * @returns {Promise<object>} 重试结果
   */
  async retryLoad(section, options) {
    const retryKey = `${section}_load`;
    const attempts = this.retryAttempts.get(retryKey) || 0;

    if (attempts >= this.maxRetryAttempts) {
      throw new Error(`已达到最大重试次数: ${this.maxRetryAttempts}`);
    }

    this.retryAttempts.set(retryKey, attempts + 1);

    // 等待一段时间后重试
    await this.delay(this.retryDelay * (attempts + 1));

    try {
      // 这里应该调用实际的配置加载逻辑
      // 由于这是错误处理器，我们模拟重试
      if (options.retryLoader) {
        const config = await options.retryLoader(section);
        this.retryAttempts.delete(retryKey); // 成功后清除重试计数
        return {
          success: true,
          config,
          message: `重试加载成功 (第${attempts + 1}次尝试)`
        };
      }
      
      throw new Error('没有提供重试加载器');
    } catch (retryError) {
      if (attempts + 1 >= this.maxRetryAttempts) {
        this.retryAttempts.delete(retryKey);
      }
      throw retryError;
    }
  }

  /**
   * 使用缓存回退
   * @param {string} section - 配置部分
   * @returns {Promise<object>} 回退结果
   */
  async useCacheFallback(section) {
    try {
      // 尝试从本地存储获取上次成功的配置
      const result = await chrome.storage.local.get(`last_good_config_${section}`);
      const lastGoodConfig = result[`last_good_config_${section}`];

      if (lastGoodConfig && lastGoodConfig.timestamp) {
        const age = Date.now() - lastGoodConfig.timestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (age < maxAge) {
          return {
            success: true,
            config: lastGoodConfig.config,
            message: `使用缓存配置 (${Math.round(age / 1000 / 60)}分钟前)`
          };
        }
      }

      throw new Error('没有可用的缓存配置或配置已过期');
    } catch (error) {
      throw new Error(`缓存回退失败: ${error.message}`);
    }
  }

  /**
   * 从备份恢复
   * @param {string} section - 配置部分
   * @returns {Promise<object>} 恢复结果
   */
  async restoreFromBackup(section) {
    try {
      const result = await chrome.storage.local.get(`backup_config_${section}`);
      const backupConfig = result[`backup_config_${section}`];

      if (backupConfig) {
        return {
          success: true,
          config: backupConfig,
          message: '从备份恢复配置'
        };
      }

      throw new Error('没有可用的备份配置');
    } catch (error) {
      throw new Error(`备份恢复失败: ${error.message}`);
    }
  }

  /**
   * 部分恢复
   * @param {string} section - 配置部分
   * @param {Error} error - 原始错误
   * @returns {Promise<object>} 恢复结果
   */
  async partialRecovery(section, error) {
    try {
      // 尝试加载部分配置
      const defaultConfig = this.getDefaultConfig(section);
      const result = await chrome.storage.sync.get(`${section}Config`);
      const userConfig = result[`${section}Config`] || {};

      // 合并有效的用户配置和默认配置
      const mergedConfig = { ...defaultConfig };
      
      for (const [key, value] of Object.entries(userConfig)) {
        if (value !== undefined && value !== null) {
          try {
            // 简单验证
            if (typeof defaultConfig[key] === typeof value) {
              mergedConfig[key] = value;
            }
          } catch {
            // 忽略无效的配置项
          }
        }
      }

      return {
        success: true,
        config: mergedConfig,
        message: '部分配置恢复成功'
      };
    } catch (error) {
      throw new Error(`部分恢复失败: ${error.message}`);
    }
  }

  /**
   * 重置为默认配置
   * @param {string} section - 配置部分
   * @returns {Promise<object>} 重置结果
   */
  async resetToDefaults(section) {
    const defaultConfig = this.getDefaultConfig(section);
    
    try {
      // 保存默认配置到存储
      await chrome.storage.sync.set({
        [`${section}Config`]: defaultConfig
      });

      return {
        success: true,
        config: defaultConfig,
        message: '已重置为默认配置'
      };
    } catch (error) {
      // 即使保存失败，也返回默认配置
      return {
        success: true,
        config: defaultConfig,
        message: '使用默认配置（未保存）'
      };
    }
  }

  /**
   * 记录错误
   * @param {object} errorInfo - 错误信息
   */
  recordError(errorInfo) {
    this.errorHistory.push(errorInfo);
    
    // 保持错误历史记录在限制范围内
    if (this.errorHistory.length > this.maxErrorHistory) {
      this.errorHistory.shift();
    }

    // 异步保存错误日志
    this.saveErrorLog(errorInfo).catch(error => {
      console.warn('保存错误日志失败:', error);
    });
  }

  /**
   * 保存错误日志
   * @param {object} errorInfo - 错误信息
   */
  async saveErrorLog(errorInfo) {
    try {
      const result = await chrome.storage.local.get('config_error_logs');
      const logs = result.config_error_logs || [];
      
      logs.push(errorInfo);
      
      // 只保留最近的100条错误日志
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      await chrome.storage.local.set({ config_error_logs: logs });
    } catch (error) {
      console.warn('无法保存错误日志:', error);
    }
  }

  /**
   * 获取默认配置
   * @param {string} section - 配置部分
   * @returns {object} 默认配置
   */
  getDefaultConfig(section) {
    // 这里应该从配置模式中获取默认值
    // 简化实现
    const defaults = {
      ai: {
        provider: 'openrouter',
        model: 'deepseek/deepseek-chat-v3-0324:free',
        temperature: 0.7,
        maxTokens: 1000,
        timeout: 60000
      },
      flomo: {
        apiUrl: '',
        timeout: 15000
      },
      sidepanel: {
        autoClose: true,
        autoCloseDelay: 1000,
        theme: 'light',
        fontSize: 'medium'
      },
      ui: {
        language: 'zh-CN',
        showTooltips: true,
        animationEnabled: true
      }
    };

    return defaults[section] || {};
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取错误统计
   * @returns {object} 错误统计信息
   */
  getErrorStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;

    const recentErrors = this.errorHistory.filter(error => 
      now - error.timestamp < oneHour
    );

    const dailyErrors = this.errorHistory.filter(error => 
      now - error.timestamp < oneDay
    );

    const errorsBySection = {};
    this.errorHistory.forEach(error => {
      errorsBySection[error.section] = (errorsBySection[error.section] || 0) + 1;
    });

    return {
      total: this.errorHistory.length,
      recentHour: recentErrors.length,
      dailyTotal: dailyErrors.length,
      bySection: errorsBySection,
      retryAttempts: Object.fromEntries(this.retryAttempts)
    };
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorHistory = [];
    this.retryAttempts.clear();
    console.log('✅ 错误历史已清除');
  }
}

// 创建全局错误处理器实例
export const configErrorHandler = new ConfigErrorHandler();
