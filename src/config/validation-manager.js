// 配置验证管理器
// 处理配置的验证、格式检查和错误处理

import { CONFIG_SCHEMA } from './config-schema.js';

export class ConfigValidationManager {
  constructor() {
    this.validationRules = new Map();
    this.customValidators = new Map();
    this.initializeBuiltinValidators();
  }

  /**
   * 初始化内置验证器
   */
  initializeBuiltinValidators() {
    // URL验证器
    this.addCustomValidator('url', (value) => {
      try {
        new URL(value);
        return { valid: true };
      } catch {
        return { valid: false, error: '无效的URL格式' };
      }
    });

    // 数字范围验证器
    this.addCustomValidator('numberRange', (value, min = 0, max = Infinity) => {
      if (typeof value !== 'number') {
        return { valid: false, error: '必须是数字类型' };
      }
      if (value < min || value > max) {
        return { valid: false, error: `数值必须在 ${min} 到 ${max} 之间` };
      }
      return { valid: true };
    });

    // 字符串长度验证器
    this.addCustomValidator('stringLength', (value, minLength = 0, maxLength = Infinity) => {
      if (typeof value !== 'string') {
        return { valid: false, error: '必须是字符串类型' };
      }
      if (value.length < minLength || value.length > maxLength) {
        return { valid: false, error: `字符串长度必须在 ${minLength} 到 ${maxLength} 之间` };
      }
      return { valid: true };
    });

    // 枚举值验证器
    this.addCustomValidator('enum', (value, allowedValues) => {
      if (!allowedValues.includes(value)) {
        return { valid: false, error: `值必须是以下之一: ${allowedValues.join(', ')}` };
      }
      return { valid: true };
    });

    // API密钥格式验证器
    this.addCustomValidator('apiKeyFormat', (value, provider) => {
      if (!value || typeof value !== 'string') {
        return { valid: false, error: 'API密钥不能为空' };
      }

      const patterns = {
        siliconflow: /^sk-[a-zA-Z0-9]{48}$/,
        openrouter: /^sk-or-[a-zA-Z0-9-]+$/,
        deepseek: /^sk-[a-zA-Z0-9]{32}$/,
        moonshot: /^sk-[a-zA-Z0-9]{48}$/
      };

      const pattern = patterns[provider];
      if (!pattern) {
        return { valid: false, error: `不支持的提供商: ${provider}` };
      }

      if (!pattern.test(value)) {
        return { valid: false, error: `无效的 ${provider} API密钥格式` };
      }

      return { valid: true };
    });
  }

  /**
   * 添加自定义验证器
   * @param {string} name - 验证器名称
   * @param {Function} validator - 验证函数
   */
  addCustomValidator(name, validator) {
    this.customValidators.set(name, validator);
    console.log(`✅ 自定义验证器已添加: ${name}`);
  }

  /**
   * 验证配置对象
   * @param {string} section - 配置部分
   * @param {object} config - 配置对象
   * @returns {object} 验证结果
   */
  validateConfig(section, config) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema) {
      return {
        valid: false,
        error: `未知的配置部分: ${section}`,
        details: []
      };
    }

    const result = {
      valid: true,
      error: null,
      details: [],
      warnings: []
    };

    // 验证每个配置项
    if (schema.validation) {
      for (const [key, validator] of Object.entries(schema.validation)) {
        if (config.hasOwnProperty(key)) {
          const validationResult = this.validateField(section, key, config[key], validator, config);
          
          if (!validationResult.valid) {
            result.valid = false;
            result.details.push({
              field: key,
              error: validationResult.error,
              value: config[key]
            });
          }

          if (validationResult.warning) {
            result.warnings.push({
              field: key,
              warning: validationResult.warning,
              value: config[key]
            });
          }
        }
      }
    }

    // 设置主错误信息
    if (!result.valid) {
      result.error = `${section} 配置验证失败: ${result.details.length} 个字段有错误`;
    }

    return result;
  }

  /**
   * 验证单个字段
   * @param {string} section - 配置部分
   * @param {string} field - 字段名
   * @param {any} value - 字段值
   * @param {Function} validator - 验证函数
   * @param {object} fullConfig - 完整配置对象
   * @returns {object} 验证结果
   */
  validateField(section, field, value, validator, fullConfig) {
    try {
      // 如果是函数，直接调用
      if (typeof validator === 'function') {
        const isValid = validator(value, fullConfig);
        return {
          valid: isValid,
          error: isValid ? null : `${field} 字段验证失败`
        };
      }

      // 如果是对象，可能包含更复杂的验证规则
      if (typeof validator === 'object') {
        return this.validateWithRules(section, field, value, validator, fullConfig);
      }

      return { valid: true };
    } catch (error) {
      console.error(`验证字段 ${section}.${field} 时出错:`, error);
      return {
        valid: false,
        error: `验证过程中发生错误: ${error.message}`
      };
    }
  }

  /**
   * 使用规则对象进行验证
   * @param {string} section - 配置部分
   * @param {string} field - 字段名
   * @param {any} value - 字段值
   * @param {object} rules - 验证规则
   * @param {object} fullConfig - 完整配置对象
   * @returns {object} 验证结果
   */
  validateWithRules(section, field, value, rules, fullConfig) {
    const result = { valid: true, error: null, warning: null };

    // 必填验证
    if (rules.required && (value === undefined || value === null || value === '')) {
      return { valid: false, error: `${field} 是必填字段` };
    }

    // 类型验证
    if (rules.type && value !== undefined && value !== null) {
      const actualType = typeof value;
      if (actualType !== rules.type) {
        return { valid: false, error: `${field} 必须是 ${rules.type} 类型，当前是 ${actualType}` };
      }
    }

    // 自定义验证器
    if (rules.validator && this.customValidators.has(rules.validator)) {
      const customValidator = this.customValidators.get(rules.validator);
      const customResult = customValidator(value, ...(rules.args || []));
      
      if (!customResult.valid) {
        return { valid: false, error: customResult.error };
      }
      
      if (customResult.warning) {
        result.warning = customResult.warning;
      }
    }

    // 最小值/最小长度
    if (rules.min !== undefined) {
      if (typeof value === 'number' && value < rules.min) {
        return { valid: false, error: `${field} 不能小于 ${rules.min}` };
      }
      if (typeof value === 'string' && value.length < rules.min) {
        return { valid: false, error: `${field} 长度不能小于 ${rules.min}` };
      }
    }

    // 最大值/最大长度
    if (rules.max !== undefined) {
      if (typeof value === 'number' && value > rules.max) {
        return { valid: false, error: `${field} 不能大于 ${rules.max}` };
      }
      if (typeof value === 'string' && value.length > rules.max) {
        return { valid: false, error: `${field} 长度不能大于 ${rules.max}` };
      }
    }

    // 正则表达式验证
    if (rules.pattern && typeof value === 'string') {
      if (!rules.pattern.test(value)) {
        return { valid: false, error: `${field} 格式不正确` };
      }
    }

    return result;
  }

  /**
   * 验证API配置
   * @param {object} config - AI配置
   * @returns {Promise<object>} 验证结果
   */
  async validateAIConfig(config) {
    const result = { valid: true, error: null, details: [] };

    // 基础验证
    const basicValidation = this.validateConfig('ai', config);
    if (!basicValidation.valid) {
      return basicValidation;
    }

    // 提供商特定验证
    if (config.provider) {
      const providerConfig = CONFIG_SCHEMA.ai.providers[config.provider];
      if (!providerConfig) {
        return {
          valid: false,
          error: `不支持的AI提供商: ${config.provider}`
        };
      }

      // 验证模型
      if (config.model && !providerConfig.models.includes(config.model)) {
        result.valid = false;
        result.details.push({
          field: 'model',
          error: `${config.provider} 不支持模型 ${config.model}`,
          supportedModels: providerConfig.models
        });
      }
    }

    if (!result.valid) {
      result.error = 'AI配置验证失败';
    }

    return result;
  }

  /**
   * 验证Flomo配置
   * @param {object} config - Flomo配置
   * @returns {Promise<object>} 验证结果
   */
  async validateFlomoConfig(config) {
    const result = this.validateConfig('flomo', config);

    // 额外的Flomo特定验证
    if (config.apiUrl) {
      try {
        const url = new URL(config.apiUrl);
        if (!url.hostname.includes('flomo')) {
          result.warnings = result.warnings || [];
          result.warnings.push({
            field: 'apiUrl',
            warning: 'URL似乎不是Flomo的官方域名'
          });
        }
      } catch {
        // URL验证已在基础验证中处理
      }
    }

    return result;
  }

  /**
   * 获取字段的默认值
   * @param {string} section - 配置部分
   * @param {string} field - 字段名
   * @returns {any} 默认值
   */
  getDefaultValue(section, field) {
    const schema = CONFIG_SCHEMA[section];
    return schema?.defaults?.[field];
  }

  /**
   * 获取所有默认配置
   * @param {string} section - 配置部分
   * @returns {object} 默认配置对象
   */
  getDefaults(section) {
    const schema = CONFIG_SCHEMA[section];
    return schema?.defaults || {};
  }

  /**
   * 清理配置对象（移除无效字段）
   * @param {string} section - 配置部分
   * @param {object} config - 配置对象
   * @returns {object} 清理后的配置对象
   */
  sanitizeConfig(section, config) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema || !schema.defaults) {
      return config;
    }

    const sanitized = {};
    const allowedKeys = Object.keys(schema.defaults);

    for (const key of allowedKeys) {
      if (config.hasOwnProperty(key)) {
        sanitized[key] = config[key];
      }
    }

    return sanitized;
  }
}

// 创建全局验证管理器实例
export const configValidationManager = new ConfigValidationManager();
