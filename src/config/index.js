// 配置系统主入口文件
// 统一导出所有配置相关的模块和功能

// 核心管理器
export { ConfigManager, configManager } from './config-manager.js';
export { SecurityManager, securityManager } from './security-manager.js';
export { ConfigCacheManager, configCacheManager } from './cache-manager.js';
export { ConfigValidationManager, configValidationManager } from './validation-manager.js';
export { ConfigErrorHandler, configErrorHandler } from './error-handler.js';
export { ConfigImportExportManager, configImportExportManager } from './import-export-manager.js';

// 配置模式和常量
export { 
  CONFIG_SCHEMA, 
  CONFIG_PRIORITY, 
  STORAGE_KEYS, 
  ConfigValidator 
} from './config-schema.js';

// Prompt模板管理
export { 
  PROMPT_TEMPLATES, 
  TEMPLATE_PRESETS, 
  PromptManager 
} from './prompts.js';

// 适配器
export { LegacyAdapter, legacyAdapter } from './adapters/legacy-adapter.js';

/**
 * 配置系统统一初始化函数
 * @param {object} options - 初始化选项
 * @returns {Promise<object>} 初始化结果
 */
export async function initializeConfigSystem(options = {}) {
  const {
    enableSecurity = true,
    enableCache = true,
    enableErrorHandling = true,
    enableImportExport = true,
    cacheExpiration = 5 * 60 * 1000, // 5分钟
    maxRetryAttempts = 3,
    cleanupOnInit = false
  } = options;

  console.log('🚀 开始初始化配置系统...');

  const initResult = {
    success: false,
    initialized: [],
    failed: [],
    warnings: [],
    stats: {}
  };

  try {
    // 1. 初始化安全管理器
    if (enableSecurity) {
      try {
        await securityManager.initialize();
        initResult.initialized.push('security');
        console.log('✅ 安全管理器初始化完成');
      } catch (error) {
        console.error('❌ 安全管理器初始化失败:', error);
        initResult.failed.push('security');
        initResult.warnings.push('安全功能可能不可用');
      }
    }

    // 2. 配置缓存管理器
    if (enableCache) {
      try {
        configCacheManager.setCacheExpiration(cacheExpiration);
        initResult.initialized.push('cache');
        console.log('✅ 缓存管理器配置完成');
      } catch (error) {
        console.error('❌ 缓存管理器配置失败:', error);
        initResult.failed.push('cache');
      }
    }

    // 3. 配置错误处理器
    if (enableErrorHandling) {
      try {
        configErrorHandler.maxRetryAttempts = maxRetryAttempts;
        initResult.initialized.push('errorHandler');
        console.log('✅ 错误处理器配置完成');
      } catch (error) {
        console.error('❌ 错误处理器配置失败:', error);
        initResult.failed.push('errorHandler');
      }
    }

    // 4. 初始化主配置管理器
    try {
      await configManager.initialize();
      initResult.initialized.push('configManager');
      console.log('✅ 配置管理器初始化完成');
    } catch (error) {
      console.error('❌ 配置管理器初始化失败:', error);
      initResult.failed.push('configManager');
      throw error; // 主管理器失败则整个初始化失败
    }

    // 5. 清理工作（如果启用）
    if (cleanupOnInit) {
      try {
        await performCleanup();
        initResult.initialized.push('cleanup');
        console.log('✅ 清理工作完成');
      } catch (error) {
        console.warn('⚠️ 清理工作失败:', error);
        initResult.warnings.push('清理工作失败，但不影响正常使用');
      }
    }

    // 6. 收集统计信息
    initResult.stats = await collectSystemStats();

    initResult.success = true;
    console.log('🎉 配置系统初始化完成!', initResult);

    return initResult;
  } catch (error) {
    console.error('💥 配置系统初始化失败:', error);
    initResult.success = false;
    initResult.error = error.message;
    return initResult;
  }
}

/**
 * 执行系统清理
 * @returns {Promise<void>}
 */
async function performCleanup() {
  const tasks = [];

  // 清理过期缓存
  tasks.push(configCacheManager.cleanupExpired());

  // 清理过期备份
  if (configImportExportManager) {
    tasks.push(configImportExportManager.cleanupOldBackups());
  }

  // 清理错误历史（保留最近的）
  const errorStats = configErrorHandler.getErrorStats();
  if (errorStats.total > 100) {
    configErrorHandler.clearErrorHistory();
  }

  await Promise.all(tasks);
}

/**
 * 收集系统统计信息
 * @returns {Promise<object>} 统计信息
 */
async function collectSystemStats() {
  const stats = {
    timestamp: Date.now(),
    cache: {},
    security: {},
    errors: {},
    configs: {}
  };

  try {
    // 缓存统计
    stats.cache = configCacheManager.getStats();
  } catch (error) {
    stats.cache = { error: error.message };
  }

  try {
    // 安全状态
    stats.security = await securityManager.getSecurityStatus();
  } catch (error) {
    stats.security = { error: error.message };
  }

  try {
    // 错误统计
    stats.errors = configErrorHandler.getErrorStats();
  } catch (error) {
    stats.errors = { error: error.message };
  }

  try {
    // 配置摘要
    stats.configs = await configManager.getConfigSummary();
  } catch (error) {
    stats.configs = { error: error.message };
  }

  return stats;
}

/**
 * 获取配置系统健康状态
 * @returns {Promise<object>} 健康状态
 */
export async function getSystemHealth() {
  const health = {
    overall: 'unknown',
    components: {},
    issues: [],
    recommendations: []
  };

  try {
    // 检查各组件状态
    health.components.configManager = configManager.initialized ? 'healthy' : 'unhealthy';
    health.components.security = securityManager.initialized ? 'healthy' : 'unhealthy';
    health.components.cache = configCacheManager.getStats().totalEntries >= 0 ? 'healthy' : 'unhealthy';

    // 检查错误率
    const errorStats = configErrorHandler.getErrorStats();
    if (errorStats.recentHour > 10) {
      health.components.errorRate = 'warning';
      health.issues.push('最近一小时错误率较高');
      health.recommendations.push('检查配置和网络连接');
    } else {
      health.components.errorRate = 'healthy';
    }

    // 检查安全状态
    const securityStatus = await securityManager.getSecurityStatus();
    if (!securityStatus.hasEncryptionKey) {
      health.components.encryption = 'warning';
      health.issues.push('加密密钥未初始化');
      health.recommendations.push('重新初始化安全管理器');
    } else {
      health.components.encryption = 'healthy';
    }

    // 计算整体健康状态
    const componentStates = Object.values(health.components);
    if (componentStates.every(state => state === 'healthy')) {
      health.overall = 'healthy';
    } else if (componentStates.some(state => state === 'unhealthy')) {
      health.overall = 'unhealthy';
    } else {
      health.overall = 'warning';
    }

  } catch (error) {
    health.overall = 'error';
    health.error = error.message;
  }

  return health;
}

/**
 * 重置整个配置系统
 * @param {object} options - 重置选项
 * @returns {Promise<boolean>} 重置是否成功
 */
export async function resetConfigSystem(options = {}) {
  const {
    clearCache = true,
    clearSecrets = false,
    clearBackups = false,
    clearErrors = true
  } = options;

  try {
    console.log('🔄 开始重置配置系统...');

    // 清除缓存
    if (clearCache) {
      configCacheManager.clear();
    }

    // 清除敏感数据
    if (clearSecrets) {
      await securityManager.clearAllEncryptedData();
    }

    // 清除备份
    if (clearBackups) {
      const backups = await configImportExportManager.listBackups();
      for (const backup of backups) {
        await configImportExportManager.deleteBackup(backup.id);
      }
    }

    // 清除错误历史
    if (clearErrors) {
      configErrorHandler.clearErrorHistory();
    }

    // 重新初始化
    await configManager.initialize();

    console.log('✅ 配置系统重置完成');
    return true;
  } catch (error) {
    console.error('❌ 配置系统重置失败:', error);
    return false;
  }
}

// 导出便捷的配置操作函数
export const config = {
  // 获取配置
  get: (section) => configManager.getConfig(section),
  
  // 设置配置
  set: (section, config) => configManager.setConfig(section, config),
  
  // 重置配置
  reset: (section) => configManager.resetConfig(section),
  
  // 验证配置
  validate: (section, config) => configManager.validateApiConfig(section, config),
  
  // 监听配置变更
  listen: (section, callback) => configManager.addListener(section, callback),
  
  // 导出配置
  export: (options) => configImportExportManager.exportAllConfigs(options),
  
  // 导入配置
  import: (data, options) => configImportExportManager.importConfigs(data, options),
  
  // 创建备份
  backup: () => configImportExportManager.createBackup(),
  
  // 获取系统状态
  health: () => getSystemHealth(),
  
  // 获取统计信息
  stats: () => collectSystemStats()
};

// 默认导出配置对象
export default config;
